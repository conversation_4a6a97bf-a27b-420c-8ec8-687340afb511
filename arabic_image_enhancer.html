<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محسن الصور الاحترافي - AliToucan</title>
    <meta name="description" content="أداة احترافية لتحسين جودة الصور مع دعم كامل للغة العربية وتقنيات متقدمة">
    <meta name="keywords" content="تحسين الصور, معالجة الصور, جودة الصور, عربي, احترافي">

    <!-- Arabic Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        :root {
            /* Arabic-friendly color palette */
            --primary-color: #2c5aa0;
            --secondary-color: #1e3a5f;
            --accent-color: #d4af37;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;

            /* Neutral colors */
            --white: #ffffff;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
            --black: #000000;

            /* Gradients */
            --primary-gradient: linear-gradient(135deg, #2c5aa0 0%, #1e3a5f 100%);
            --accent-gradient: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
            --dark-gradient: linear-gradient(135deg, #343a40 0%, #1e3a5f 100%);

            /* Shadows */
            --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
            --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);

            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-medium: 0.3s ease;
            --transition-slow: 0.5s ease;

            /* Border radius */
            --radius-small: 8px;
            --radius-medium: 12px;
            --radius-large: 20px;
            --radius-xl: 30px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', 'Amiri', Arial, sans-serif;
            line-height: 1.6;
            color: var(--dark-gray);
            background: var(--light-gray);
            direction: rtl;
            overflow-x: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-content {
            text-align: center;
            color: var(--white);
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid var(--white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Header */
        .header {
            background: var(--white);
            box-shadow: var(--shadow-light);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .logo i {
            font-size: 2rem;
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: var(--dark-gray);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-fast);
            padding: 0.5rem 1rem;
            border-radius: var(--radius-small);
        }

        .nav-link:hover {
            color: var(--primary-color);
            background: var(--light-gray);
        }

        /* Hero Section */
        .hero {
            background: var(--primary-gradient);
            color: var(--white);
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            font-family: 'Amiri', serif;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: var(--accent-gradient);
            color: var(--white);
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--radius-medium);
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-medium);
            box-shadow: var(--shadow-medium);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
        }

        /* Main Content */
        .main-content {
            padding: 3rem 0;
        }

        .enhancement-panel {
            background: var(--white);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-medium);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .panel-header {
            background: var(--primary-gradient);
            color: var(--white);
            padding: 1.5rem;
            text-align: center;
        }

        .panel-header h2 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-family: 'Amiri', serif;
        }

        .panel-content {
            padding: 2rem;
        }

        /* Upload Area */
        .upload-area {
            border: 3px dashed var(--medium-gray);
            border-radius: var(--radius-medium);
            padding: 3rem;
            text-align: center;
            transition: var(--transition-medium);
            cursor: pointer;
            margin-bottom: 2rem;
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(44, 90, 160, 0.05);
        }

        .upload-area.has-file {
            border-color: var(--success-color);
            background: rgba(40, 167, 69, 0.05);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--medium-gray);
            margin-bottom: 1rem;
        }

        .upload-area.dragover .upload-icon,
        .upload-area.has-file .upload-icon {
            color: var(--primary-color);
        }

        .upload-text {
            font-size: 1.1rem;
            color: var(--medium-gray);
            margin-bottom: 1rem;
        }

        .file-input {
            display: none;
        }

        .upload-button {
            background: var(--primary-color);
            color: var(--white);
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: var(--radius-small);
            cursor: pointer;
            font-size: 1rem;
            transition: var(--transition-fast);
        }

        .upload-button:hover {
            background: var(--secondary-color);
        }

        /* Image Preview */
        .image-preview {
            display: none;
            margin-bottom: 2rem;
        }

        .preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .preview-box {
            background: var(--light-gray);
            border-radius: var(--radius-medium);
            padding: 1rem;
            text-align: center;
        }

        .preview-box h3 {
            margin-bottom: 1rem;
            color: var(--dark-gray);
            font-weight: 600;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: var(--radius-small);
            box-shadow: var(--shadow-light);
        }

        /* Controls Panel */
        .controls-panel {
            background: var(--light-gray);
            border-radius: var(--radius-medium);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .control-group {
            background: var(--white);
            padding: 1.5rem;
            border-radius: var(--radius-medium);
            box-shadow: var(--shadow-light);
        }

        .control-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-gray);
        }

        .control-slider {
            width: 100%;
            margin-bottom: 1rem;
        }

        .slider-value {
            text-align: center;
            font-weight: 600;
            color: var(--primary-color);
            background: var(--light-gray);
            padding: 0.5rem;
            border-radius: var(--radius-small);
        }

        /* Preset Filters */
        .preset-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .preset-btn {
            background: var(--white);
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 1rem;
            border-radius: var(--radius-medium);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition-fast);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .preset-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .preset-btn i {
            font-size: 1.5rem;
        }

        /* Action Buttons */
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 2rem;
        }

        .action-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--radius-medium);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-fast);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1rem;
        }

        .reset-btn {
            background: var(--medium-gray);
            color: var(--white);
        }

        .reset-btn:hover {
            background: var(--dark-gray);
            transform: translateY(-2px);
        }

        .download-btn {
            background: var(--success-color);
            color: var(--white);
        }

        .download-btn:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Features Section */
        .features-section {
            background: var(--white);
            padding: 4rem 0;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: var(--light-gray);
            padding: 2rem;
            border-radius: var(--radius-large);
            text-align: center;
            transition: var(--transition-medium);
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
            border-color: var(--primary-color);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--dark-gray);
            font-family: 'Amiri', serif;
        }

        .feature-description {
            color: var(--medium-gray);
            line-height: 1.6;
        }

        /* Footer */
        .footer {
            background: var(--dark-gradient);
            color: var(--white);
            padding: 3rem 0 2rem;
            text-align: center;
        }

        .footer-content {
            margin-bottom: 2rem;
        }

        .footer-logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--accent-color);
            font-family: 'Amiri', serif;
        }

        .footer-description {
            max-width: 600px;
            margin: 0 auto 2rem;
            opacity: 0.9;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-link {
            color: var(--white);
            text-decoration: none;
            transition: var(--transition-fast);
        }

        .footer-link:hover {
            color: var(--accent-color);
        }

        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.2);
            padding-top: 2rem;
            opacity: 0.7;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .preview-container {
                grid-template-columns: 1fr;
            }

            .controls-grid {
                grid-template-columns: 1fr;
            }

            .preset-filters {
                grid-template-columns: repeat(2, 1fr);
            }

            .action-buttons {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2>جاري تحميل محسن الصور الاحترافي</h2>
            <p>يرجى الانتظار...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">
                    <i class="fas fa-image"></i>
                    محسن الصور الاحترافي
                </a>
                <nav>
                    <ul class="nav-links">
                        <li><a href="#home" class="nav-link">الرئيسية</a></li>
                        <li><a href="#features" class="nav-link">المميزات</a></li>
                        <li><a href="#help" class="nav-link">المساعدة</a></li>
                        <li><a href="#contact" class="nav-link">التواصل</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content animate__animated animate__fadeInUp">
                <h1>محسن الصور الاحترافي</h1>
                <p>أداة متقدمة لتحسين جودة الصور مع دعم كامل للغة العربية وتقنيات احترافية لمعالجة الصور</p>
                <a href="#enhancer" class="cta-button">
                    <i class="fas fa-magic"></i>
                    ابدأ التحسين الآن
                </a>
            </div>
        </div>
    </section>

    <!-- Main Enhancement Panel -->
    <section class="main-content" id="enhancer">
        <div class="container">
            <div class="enhancement-panel animate__animated animate__fadeInUp">
                <div class="panel-header">
                    <h2>رفع وتحسين الصور</h2>
                    <p>اختر صورتك وابدأ في تحسين جودتها باستخدام أدواتنا المتقدمة</p>
                </div>
                <div class="panel-content">
                    <!-- Upload Area -->
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            <strong>اسحب وأفلت الصورة هنا</strong><br>
                            أو انقر لاختيار ملف من جهازك
                        </div>
                        <input type="file" class="file-input" id="fileInput" accept="image/*" multiple>
                        <button class="upload-button" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open"></i>
                            اختيار الصور
                        </button>
                        <div style="margin-top: 1rem; font-size: 0.9rem; color: var(--medium-gray);">
                            الصيغ المدعومة: JPG, PNG, WebP, GIF, BMP (حتى 10 ميجابايت)
                        </div>
                    </div>

                    <!-- Image Preview -->
                    <div class="image-preview" id="imagePreview">
                        <div class="preview-container">
                            <div class="preview-box">
                                <h3>الصورة الأصلية</h3>
                                <img id="originalImage" class="preview-image" alt="الصورة الأصلية">
                            </div>
                            <div class="preview-box">
                                <h3>الصورة المحسنة</h3>
                                <img id="enhancedImage" class="preview-image" alt="الصورة المحسنة">
                            </div>
                        </div>
                    </div>

                    <!-- Controls Panel -->
                    <div class="controls-panel" id="controlsPanel" style="display: none;">
                        <h3 style="text-align: center; margin-bottom: 2rem; color: var(--primary-color); font-family: 'Amiri', serif;">
                            <i class="fas fa-sliders-h"></i>
                            أدوات التحسين المتقدمة
                        </h3>
                        <div class="controls-grid">
                            <div class="control-group">
                                <label class="control-label">
                                    <i class="fas fa-sun"></i>
                                    السطوع
                                </label>
                                <input type="range" class="control-slider" id="brightnessSlider" min="-100" max="100" value="0">
                                <div class="slider-value" id="brightnessValue">0</div>
                            </div>

                            <div class="control-group">
                                <label class="control-label">
                                    <i class="fas fa-adjust"></i>
                                    التباين
                                </label>
                                <input type="range" class="control-slider" id="contrastSlider" min="-100" max="100" value="0">
                                <div class="slider-value" id="contrastValue">0</div>
                            </div>

                            <div class="control-group">
                                <label class="control-label">
                                    <i class="fas fa-palette"></i>
                                    التشبع
                                </label>
                                <input type="range" class="control-slider" id="saturationSlider" min="-100" max="100" value="0">
                                <div class="slider-value" id="saturationValue">0</div>
                            </div>

                            <div class="control-group">
                                <label class="control-label">
                                    <i class="fas fa-eye"></i>
                                    الحدة
                                </label>
                                <input type="range" class="control-slider" id="sharpnessSlider" min="0" max="200" value="100">
                                <div class="slider-value" id="sharpnessValue">100%</div>
                            </div>
                        </div>

                        <!-- Preset Filters -->
                        <div style="margin-top: 2rem;">
                            <h4 style="text-align: center; margin-bottom: 1rem; color: var(--dark-gray);">
                                <i class="fas fa-magic"></i>
                                فلاتر جاهزة
                            </h4>
                            <div class="preset-filters">
                                <button class="preset-btn" onclick="imageEnhancer.applyPreset('portrait')">
                                    <i class="fas fa-user"></i>
                                    صورة شخصية
                                </button>
                                <button class="preset-btn" onclick="imageEnhancer.applyPreset('landscape')">
                                    <i class="fas fa-mountain"></i>
                                    منظر طبيعي
                                </button>
                                <button class="preset-btn" onclick="imageEnhancer.applyPreset('social')">
                                    <i class="fas fa-share-alt"></i>
                                    وسائل التواصل
                                </button>
                                <button class="preset-btn" onclick="imageEnhancer.applyPreset('vintage')">
                                    <i class="fas fa-camera-retro"></i>
                                    كلاسيكي
                                </button>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="action-btn reset-btn" onclick="imageEnhancer.resetEnhancements()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <button class="action-btn download-btn" onclick="imageEnhancer.downloadImage()">
                                <i class="fas fa-download"></i>
                                تحميل الصورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="features">
        <div class="container">
            <div class="text-center">
                <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: var(--primary-color); font-family: 'Amiri', serif;">
                    مميزات محسن الصور الاحترافي
                </h2>
                <p style="font-size: 1.2rem; color: var(--medium-gray); max-width: 600px; margin: 0 auto;">
                    أدوات متقدمة لتحسين جودة الصور مع دعم كامل للغة العربية وتقنيات احترافية
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-card animate__animated animate__fadeInUp">
                    <div class="feature-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3 class="feature-title">تحسين تلقائي ذكي</h3>
                    <p class="feature-description">
                        خوارزميات متقدمة لتحسين السطوع والتباين والألوان تلقائياً مع الحفاظ على جودة الصورة الأصلية
                    </p>
                </div>

                <div class="feature-card animate__animated animate__fadeInUp">
                    <div class="feature-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3 class="feature-title">دعم كامل للعربية</h3>
                    <p class="feature-description">
                        واجهة مصممة خصيصاً للمستخدمين العرب مع دعم RTL وخطوط عربية عالية الجودة
                    </p>
                </div>

                <div class="feature-card animate__animated animate__fadeInUp">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">تصميم متجاوب</h3>
                    <p class="feature-description">
                        يعمل بشكل مثالي على جميع الأجهزة - الهواتف الذكية والأجهزة اللوحية وأجهزة الكمبيوتر
                    </p>
                </div>

                <div class="feature-card animate__animated animate__fadeInUp">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title">معالجة سريعة</h3>
                    <p class="feature-description">
                        تقنيات متقدمة لمعالجة الصور بسرعة عالية مع الحفاظ على أعلى مستويات الجودة
                    </p>
                </div>

                <div class="feature-card animate__animated animate__fadeInUp">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">أمان وخصوصية</h3>
                    <p class="feature-description">
                        جميع الصور تتم معالجتها محلياً في متصفحك - لا يتم رفع أي صور إلى الخوادم الخارجية
                    </p>
                </div>

                <div class="feature-card animate__animated animate__fadeInUp">
                    <div class="feature-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3 class="feature-title">تحميل بصيغ متعددة</h3>
                    <p class="feature-description">
                        إمكانية تحميل الصور المحسنة بصيغ مختلفة (JPG, PNG, WebP) مع جودة قابلة للتخصيص
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <i class="fas fa-image"></i>
                    محسن الصور الاحترافي
                </div>
                <p class="footer-description">
                    أداة احترافية لتحسين جودة الصور مطورة خصيصاً للمستخدمين العرب.
                    نوفر تقنيات متقدمة لمعالجة الصور مع واجهة سهلة الاستخدام ودعم كامل للغة العربية.
                </p>
                <div class="footer-links">
                    <a href="#home" class="footer-link">الرئيسية</a>
                    <a href="#features" class="footer-link">المميزات</a>
                    <a href="#help" class="footer-link">المساعدة</a>
                    <a href="#contact" class="footer-link">التواصل</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 AliToucan - محسن الصور الاحترافي. جميع الحقوق محفوظة.</p>
                <p style="margin-top: 0.5rem; font-size: 0.9rem;">
                    مطور بعناية للمجتمع العربي 🇮🇶
                </p>
            </div>
        </div>
    </footer>

    <script src="arabic_image_enhancer.js"></script>
</body>
</html>
